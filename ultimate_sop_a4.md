```mermaid
flowchart LR
    %% 核心哲学
    START["🧠 理科学习终极SOP<br/>从'解题'到'破局'<br/>从'知识'到'智慧'"]
    
    %% 阶段0：奠基
    subgraph S0 ["🏗️ 阶段0：奠基"]
        direction TB
        S0_1["🔍 主动探索<br/>预测内容<br/>识别根本问题"]
        S0_2["🎓 费曼技巧<br/>讲授→堵漏→类比<br/>如：熵→房间混乱"]
        S0_3["🕸️ 知识网络<br/>从哪来？到哪去？<br/>和谁并列？"]
        S0_4["🔄 间隔重复<br/>Anki卡片<br/>主动回忆"]
        S0_1 --> S0_2 --> S0_3 --> S0_4
    end
    
    %% 阶段1：破题
    subgraph S1 ["🔓 阶段1：破题"]
        direction TB
        S1_1["📊 信息分层<br/>表层/深层/噪音<br/>净化问题"]
        S1_2["❓ 终极追问<br/>第一性原理<br/>回归基本公理"]
        S1_3["🎯 系统建模<br/>组件→关系→图示<br/>结构与流动"]
        S1_1 --> S1_2 --> S1_3
    end
    
    %% 阶段2：谋略
    subgraph S2 ["🎯 阶段2：谋略"]
        direction TB
        S2_0["🤔 问题类型判断"]
        
        subgraph S2_DUAL ["双轨制路径"]
            S2_A["🚄 轨道A：经验驱动<br/>模式识别→知识检索<br/>快速验证"]
            S2_B["🚀 轨道B：原理驱动<br/>抛弃类比→基石重构<br/>思想实验"]
        end
        
        S2_C["🔄 双轨融合<br/>最优策略"]
        
        S2_0 --> S2_A
        S2_0 --> S2_B
        S2_A --> S2_C
        S2_B --> S2_C
    end
    
    %% 阶段3：执行
    subgraph S3 ["⚡ 阶段3：执行"]
        direction TB
        S3_1["🧩 模块化执行<br/>分解→验证→推进<br/>独立子模块"]
        S3_2["🧠 认知负荷管理<br/>思考/计算分离<br/>草稿纸分区"]
        S3_3["🔍 持续审查<br/>量纲/数量级/符号<br/>实时纠错"]
        S3_1 --> S3_2 --> S3_3
    end
    
    %% 阶段4：升维
    subgraph S4 ["🚀 阶段4：升维"]
        direction TB
        S4_1["🔍 多维验证<br/>对称性/极端情况<br/>逆向工程"]
        S4_2["💎 方法萃取<br/>找解题'钥匙'<br/>识别巧妙假设"]
        S4_3["🎨 抽象建模<br/>一句话总结<br/>模型命名"]
        S4_4["🌟 知识迁移<br/>其他领域应用<br/>刻意练习指令"]
        S4_1 --> S4_2 --> S4_3 --> S4_4
    end
    
    %% 认知工具箱
    subgraph TOOLS ["🛠️ 认知工具箱"]
        direction TB
        
        T_COG["🧠 认知工具<br/>费曼技巧<br/>思维导图<br/>间隔重复"]
        T_ANA["📊 分析工具<br/>第一性原理<br/>系统思维<br/>思想实验"]
        T_EXE["⚡ 执行工具<br/>模块化<br/>多维验证<br/>刻意练习"]
        
        T_COG --> T_ANA --> T_EXE
    end
    
    %% 升维反馈系统
    subgraph FEEDBACK ["🔄 升维反馈系统"]
        direction TB
        F1["📚 知识网络更新<br/>新模式整合"]
        F2["🎯 策略库扩充<br/>解题模板优化"]
        F3["🧠 心智模型进化<br/>认知框架升级"]
        F1 --> F2 --> F3
    end
    
    %% 主流程
    START --> S0
    S0 --> S1
    S1 --> S2
    S2 --> S3
    S3 --> S4
    
    %% 工具箱连接
    S0 -.-> T_COG
    S1 -.-> T_ANA
    S2 -.-> T_ANA
    S3 -.-> T_EXE
    S4 -.-> T_EXE
    
    %% 升维反馈
    S4 --> FEEDBACK
    FEEDBACK -.->|知识网络| S0
    FEEDBACK -.->|策略优化| S2
    FEEDBACK -.->|认知升级| START
    
    %% 质量控制检查点
    S0 --> C0{奠基完成？}
    S1 --> C1{问题解码？}
    S2 --> C2{策略确定？}
    S3 --> C3{执行完成？}
    S4 --> C4{升维成功？}
    
    C0 -->|否| S0
    C1 -->|否| S1
    C2 -->|否| S2
    C3 -->|否| S3
    C4 -->|否| S4
    
    C0 -->|是| S1
    C1 -->|是| S2
    C2 -->|是| S3
    C3 -->|是| S4
    C4 -->|是| END([🏆 心智模型升级<br/>智慧内化完成])
    
    %% 样式定义
    classDef startEnd fill:#ff6b6b,stroke:#c92a2a,stroke-width:3px,color:#fff,font-weight:bold
    classDef stage fill:#4ecdc4,stroke:#26a69a,stroke-width:2px,color:#000
    classDef tool fill:#fd79a8,stroke:#e84393,stroke-width:2px,color:#000
    classDef feedback fill:#a29bfe,stroke:#6c5ce7,stroke-width:2px,color:#000
    classDef check fill:#f9ca24,stroke:#f0932b,stroke-width:2px,color:#000
    
    class START,END startEnd
    class S0_1,S0_2,S0_3,S0_4,S1_1,S1_2,S1_3,S2_0,S2_A,S2_B,S2_C,S3_1,S3_2,S3_3,S4_1,S4_2,S4_3,S4_4 stage
    class T_COG,T_ANA,T_EXE tool
    class F1,F2,F3 feedback
    class C0,C1,C2,C3,C4 check
```
