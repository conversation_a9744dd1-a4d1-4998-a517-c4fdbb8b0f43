```mermaid
flowchart TD
    %% 核心哲学
    PHILOSOPHY["🧠 核心哲学<br/>从'解题'到'破局'<br/>从'知识'到'智慧'<br/>在没有路的地方开辟新路"]
    
    %% 阶段0：奠基
    subgraph STAGE0 ["🏗️ 阶段0：奠基 (Foundation)<br/>深度认知与知识体系构建"]
        direction TB
        
        S0_1["🔍 主动探索与第一印象<br/>• 预测章节内容<br/>• 识别根本问题<br/>• 带假设阅读"]
        
        subgraph S0_FEYNMAN ["🎓 费曼技巧驱动理解"]
            S0_2A["📚 讲授<br/>用简单语言<br/>教给初中生"]
            S0_2B["🔧 堵漏<br/>找薄弱环节<br/>重新学习"]
            S0_2C["🔗 简化类比<br/>寻找绝佳类比<br/>如：熵→房间混乱"]
        end
        
        subgraph S0_NETWORK ["🕸️ 构建知识网络"]
            S0_3A["⬆️ 它从哪里来？<br/>更基本原理的推论"]
            S0_3B["⬇️ 它到哪里去？<br/>能推导什么现象"]
            S0_3C["↔️ 它和谁并列？<br/>并列或对立关系"]
        end
        
        S0_4["🔄 间隔重复与主动回忆<br/>• Anki卡片系统<br/>• 先想后看<br/>• 强化记忆"]
        
        S0_1 --> S0_FEYNMAN
        S0_FEYNMAN --> S0_NETWORK
        S0_NETWORK --> S0_4
    end
    
    %% 阶段1：破题
    subgraph STAGE1 ["🔓 阶段1：破题 (Deconstruction)<br/>第一性原理视角下的问题解码"]
        direction TB
        
        subgraph S1_INFO ["📊 信息分层与净化"]
            S1_1A["表层：已知数、未知量"]
            S1_1B["深层：隐含条件、约束"]
            S1_1C["噪音：无关数据识别"]
        end
        
        subgraph S1_WHY ["❓ 终极追问 (第一性原理)"]
            S1_2A["反复追问'为什么'"]
            S1_2B["回归基本公理定律"]
            S1_2C["找到底层交互机制"]
        end
        
        subgraph S1_SYSTEM ["🎯 系统思维建模"]
            S1_3A["🧩 识别组件<br/>参与者分析"]
            S1_3B["🔗 识别关系<br/>相互作用方式"]
            S1_3C["📈 画系统图<br/>结构与流动"]
        end
        
        S1_INFO --> S1_WHY
        S1_WHY --> S1_SYSTEM
    end
    
    %% 阶段2：谋略
    subgraph STAGE2 ["🎯 阶段2：谋略 (Strategy)<br/>双轨制路径规划"]
        direction TB
        
        subgraph TRACK_A ["🚄 轨道A：类比/经验驱动"]
            S2_A1["🔍 模式识别<br/>找模板题相似性"]
            S2_A2["📚 知识检索<br/>调取相关解法"]
            S2_A3["✅ 快速验证<br/>条件满足检查"]
        end
        
        subgraph TRACK_B ["🚀 轨道B：第一性原理驱动"]
            S2_B1["🗑️ 抛弃类比<br/>回到基本公理"]
            S2_B2["🏗️ 从基石重构<br/>逻辑严密推导"]
            S2_B3["🧪 思想实验<br/>'如果...会怎样？'"]
        end
        
        S2_CHOICE{"🤔 问题类型判断"}
        S2_CHOICE -->|标准问题| TRACK_A
        S2_CHOICE -->|新颖复杂| TRACK_B
        
        TRACK_A --> S2_MERGE["🔄 双轨融合"]
        TRACK_B --> S2_MERGE
    end
    
    %% 阶段3：执行
    subgraph STAGE3 ["⚡ 阶段3：执行 (Execution)<br/>精准操作与认知负荷管理"]
        direction TB
        
        S3_1["🧩 模块化执行<br/>• 分解子模块<br/>• 逐一验证<br/>• 独立完成"]
        
        S3_2["🧠 认知负荷管理<br/>• 思考与计算分离<br/>• 草稿纸分区<br/>• 避免频繁切换"]
        
        subgraph S3_AUDIT ["🔍 持续自我审查"]
            S3_3A["📏 量纲审查<br/>物理化学金标准"]
            S3_3B["📊 数量级审查<br/>结果合理性"]
            S3_3C["➡️ 符号审查<br/>正负号方向"]
        end
        
        S3_1 --> S3_2
        S3_2 --> S3_AUDIT
    end
    
    %% 阶段4：升维
    subgraph STAGE4 ["🚀 阶段4：升维 (Ascension)<br/>从解题到心智模型构建"]
        direction TB
        
        S4_1["🔍 多维度验证<br/>• 对称性检验<br/>• 极端情况<br/>• 逆向工程"]
        
        S4_2["💎 方法论萃取<br/>• 找到解题'钥匙'<br/>• 识别巧妙假设<br/>• 视角转换点"]
        
        subgraph S4_ABSTRACT ["🎨 抽象与建模"]
            S4_3A["📝 一句话总结<br/>问题本质描述"]
            S4_3B["🏷️ 模型命名<br/>便于记忆标签"]
        end
        
        subgraph S4_TRANSFER ["🌟 知识迁移与练习指令"]
            S4_4A["🔄 迁移应用<br/>其他领域应用"]
            S4_4B["🎯 刻意练习指令<br/>具体可执行目标"]
        end
        
        S4_1 --> S4_2
        S4_2 --> S4_ABSTRACT
        S4_ABSTRACT --> S4_TRANSFER
    end
    
    %% 认知工具箱
    subgraph TOOLBOX ["🛠️ 认知工具箱"]
        direction LR
        
        subgraph T_COGNITIVE ["🧠 认知工具"]
            T1["🔍 费曼技巧"]
            T2["🕸️ 思维导图"]
            T3["🔄 间隔重复"]
        end
        
        subgraph T_ANALYSIS ["📊 分析工具"]
            T4["❓ 第一性原理"]
            T5["🎯 系统思维"]
            T6["🧪 思想实验"]
        end
        
        subgraph T_EXECUTION ["⚡ 执行工具"]
            T7["🧩 模块化"]
            T8["🔍 多维验证"]
            T9["🎯 刻意练习"]
        end
    end
    
    %% 主流程连接
    PHILOSOPHY --> STAGE0
    STAGE0 --> STAGE1
    STAGE1 --> STAGE2
    STAGE2 --> STAGE3
    STAGE3 --> STAGE4
    
    %% 升维反馈循环
    STAGE4 -.->|知识网络更新| STAGE0
    STAGE4 -.->|模式库扩充| STAGE2
    
    %% 工具箱连接
    STAGE0 -.-> T_COGNITIVE
    STAGE1 -.-> T_ANALYSIS
    STAGE2 -.-> T_ANALYSIS
    STAGE3 -.-> T_EXECUTION
    STAGE4 -.-> T_EXECUTION
    
    %% 样式定义
    classDef philosophy fill:#ff6b6b,stroke:#c92a2a,stroke-width:3px,color:#fff,font-weight:bold
    classDef foundation fill:#4ecdc4,stroke:#26a69a,stroke-width:2px,color:#000
    classDef deconstruction fill:#45b7d1,stroke:#2196f3,stroke-width:2px,color:#000
    classDef strategy fill:#f9ca24,stroke:#f0932b,stroke-width:2px,color:#000
    classDef execution fill:#6c5ce7,stroke:#5f3dc4,stroke-width:2px,color:#fff
    classDef ascension fill:#a29bfe,stroke:#6c5ce7,stroke-width:2px,color:#000
    classDef tool fill:#fd79a8,stroke:#e84393,stroke-width:2px,color:#000
    
    class PHILOSOPHY philosophy
    class S0_1,S0_2A,S0_2B,S0_2C,S0_3A,S0_3B,S0_3C,S0_4 foundation
    class S1_1A,S1_1B,S1_1C,S1_2A,S1_2B,S1_2C,S1_3A,S1_3B,S1_3C deconstruction
    class S2_A1,S2_A2,S2_A3,S2_B1,S2_B2,S2_B3,S2_CHOICE,S2_MERGE strategy
    class S3_1,S3_2,S3_3A,S3_3B,S3_3C execution
    class S4_1,S4_2,S4_3A,S4_3B,S4_4A,S4_4B ascension
    class T1,T2,T3,T4,T5,T6,T7,T8,T9 tool
```
