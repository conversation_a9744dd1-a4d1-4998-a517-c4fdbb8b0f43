```mermaid
flowchart LR
    %% 开始
    START([🎯 数学解题SOP<br/>波利亚四阶段法])
    
    %% 第一阶段：理解题目
    subgraph S1 ["📖 阶段1：理解题目"]
        direction TB
        S1_1["❓ 核心三问<br/>• 未知量是什么？<br/>• 已知数据是什么？<br/>• 条件是什么？"]
        S1_2["🔍 深入分析<br/>• 条件充分性检查<br/>• 识别矛盾或冗余<br/>• 明确求解目标"]
        S1_3["🛠️ 理解工具<br/>• 画图可视化<br/>• 引入符号记号<br/>• 分离条件要素"]
        S1_4["✅ 检查点<br/>问题结构化完成？"]
        
        S1_1 --> S1_2 --> S1_3 --> S1_4
    end
    
    %% 第二阶段：拟定方案
    subgraph S2 ["🧠 阶段2：拟定方案"]
        direction TB
        S2_1["🔄 回忆联想<br/>• 见过类似问题？<br/>• 相关定理方法？<br/>• 已解决的相关题？"]
        S2_2["💡 启发技巧<br/>• 特殊化简化<br/>• 一般化扩展<br/>• 类比相似结构"]
        S2_3["🔧 策略工具<br/>• 逆向工作法<br/>• 分解子问题<br/>• 引入辅助元素"]
        S2_4["📋 制定计划<br/>• 选择解题路径<br/>• 确定具体步骤<br/>• 预估可行性"]
        
        S2_1 --> S2_2 --> S2_3 --> S2_4
    end
    
    %% 第三阶段：执行方案
    subgraph S3 ["⚡ 阶段3：执行方案"]
        direction TB
        S3_1["📝 逐步执行<br/>• 按计划实施<br/>• 保持逻辑严密<br/>• 记录关键步骤"]
        S3_2["🔍 实时检验<br/>• 每步都要验证<br/>• 确保推理正确<br/>• 计算准确无误"]
        S3_3["⚠️ 质量控制<br/>• 及时发现错误<br/>• 纠正偏差<br/>• 保持警惕性"]
        S3_4["✅ 完成检查<br/>得到初步答案？"]
        
        S3_1 --> S3_2 --> S3_3 --> S3_4
    end
    
    %% 第四阶段：回顾检验
    subgraph S4 ["🔍 阶段4：回顾检验"]
        direction TB
        S4_1["✅ 结果验证<br/>• 检验答案正确性<br/>• 验证推理过程<br/>• 尝试其他方法"]
        S4_2["🎓 反思总结<br/>• 总结解题思路<br/>• 识别关键技巧<br/>• 记录经验教训"]
        S4_3["🌟 知识迁移<br/>• 方法可复用性<br/>• 推广到类似题<br/>• 形成解题模式"]
        S4_4["📈 能力提升<br/>解题技能增强？"]
        
        S4_1 --> S4_2 --> S4_3 --> S4_4
    end
    
    %% 技巧工具箱
    subgraph TOOLS ["🛠️ 波利亚技巧工具箱"]
        direction TB
        
        subgraph T1 ["认知技巧"]
            T1_1["🔍 类比法"]
            T1_2["📊 特殊化"]
            T1_3["🌐 一般化"]
        end
        
        subgraph T2 ["操作技巧"]
            T2_1["🧩 分解法"]
            T2_2["⬅️ 逆向法"]
            T2_3["🎨 可视化"]
        end
        
        subgraph T3 ["系统技巧"]
            T3_1["➕ 辅助元素"]
            T3_2["🔄 试验检验"]
            T3_3["📝 问题重述"]
        end
    end
    
    %% 质量保证循环
    subgraph QA ["🔄 质量保证机制"]
        direction TB
        QA_1["❌ 理解不清？<br/>返回阶段1"]
        QA_2["❌ 方案不可行？<br/>返回阶段2"]
        QA_3["❌ 执行有误？<br/>返回阶段3"]
        QA_4["❌ 结果错误？<br/>重新检验"]
    end
    
    %% 结束
    END([🏆 解题完成<br/>知识内化])
    
    %% 主流程
    START --> S1
    S1 --> S2
    S2 --> S3
    S3 --> S4
    S4 --> END
    
    %% 质量控制反馈
    S1_4 -->|否| S1_1
    S2_4 -->|不可行| S1
    S3_4 -->|否| S2
    S4_4 -->|需改进| S3
    
    %% 工具箱连接
    S2 -.-> TOOLS
    S3 -.-> TOOLS
    
    %% 样式定义
    classDef start fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px,color:#000,font-weight:bold
    classDef stage fill:#e3f2fd,stroke:#1565c0,stroke-width:2px,color:#000
    classDef tool fill:#fff8e1,stroke:#f57f17,stroke-width:2px,color:#000
    classDef qa fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#000
    classDef end fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px,color:#000,font-weight:bold
    
    class START,END start
    class S1_1,S1_2,S1_3,S1_4,S2_1,S2_2,S2_3,S2_4,S3_1,S3_2,S3_3,S3_4,S4_1,S4_2,S4_3,S4_4 stage
    class T1_1,T1_2,T1_3,T2_1,T2_2,T2_3,T3_1,T3_2,T3_3 tool
    class QA_1,QA_2,QA_3,QA_4 qa
```
