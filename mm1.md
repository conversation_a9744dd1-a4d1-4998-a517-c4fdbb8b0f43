```mermaid
flowchart LR
    %% 第一阶段：理解题目
    subgraph S1 ["🎯 理解题目"]
        direction TB
        A["开始<br/>比较a,b,c大小"]
        B["分析<br/>a,b,c表达式"]
        C["目标<br/>化简后比较"]
        A --> B --> C
    end

    %% 第二阶段：观察分析
    subgraph S2 ["🔍 观察分析"]
        direction TB
        D1["观察a<br/>2021×2022-2021²<br/>有公因式2021"]
        D2["观察b<br/>1013×1008-1012×1007<br/>数字两两相邻"]
        D3["观察c<br/>√(2019²+2020+2021)<br/>根号加连续整数"]
    end

    %% 第三阶段：制定策略
    subgraph S3 ["📋 制定策略"]
        direction TB
        E1["策略a<br/>提取公因式"]
        E2["策略b<br/>变量代换<br/>x=1012, y=1007"]
        E3["策略c<br/>变量代换<br/>n=2020"]
    end

    %% 第四阶段：执行计算
    subgraph S4 ["⚡ 执行计算"]
        direction TB
        F1["计算a<br/>2021×(2022-2021)<br/>=2021×1=2021"]
        F2["计算b<br/>(x+1)×(y+1)-xy<br/>=x+y+1=2020"]
        F3["计算c<br/>√((n-1)²+n+(n+1))<br/>=√(n²+2)"]
    end

    %% 第五阶段：比较结果
    subgraph S5 ["📊 比较结果"]
        direction TB
        G["初步结果<br/>a=2021<br/>b=2020<br/>c=√(2020²+2)"]
        H["大小比较<br/>2020 < √(2020²+2) < 2021"]
        I["最终结论<br/>b < c < a"]
        G --> H --> I
    end

    %% 第六阶段：总结模式
    subgraph S6 ["🎓 总结模式"]
        direction TB
        J1["模式a<br/>n×(n+1)-n²=n"]
        J2["模式b<br/>(x+1)×(y+1)-xy=x+y+1"]
        J3["模式c<br/>√((n-1)²+n+(n+1))=√(n²+2)"]
        K["形成可复用知识"]
        J1 --> K
        J2 --> K
        J3 --> K
    end

    %% 连接各阶段
    C --> D1
    C --> D2
    C --> D3

    D1 --> E1
    D2 --> E2
    D3 --> E3

    E1 --> F1
    E2 --> F2
    E3 --> F3

    F1 --> G
    F2 --> G
    F3 --> G

    I --> J1
    I --> J2
    I --> J3

    %% 样式设置
    classDef startEnd fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#000
    classDef process fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000
    classDef result fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#000
    classDef pattern fill:#fff8e1,stroke:#f57f17,stroke-width:2px,color:#000

    class A,C,I,K startEnd
    class D1,D2,D3,E1,E2,E3,F1,F2,F3 process
    class B,G,H decision
    class J1,J2,J3 pattern
```