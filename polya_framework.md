```mermaid
flowchart TD
    %% 开始节点
    START([🎯 开始解题<br/>遇到数学问题])
    
    %% 第一阶段：理解题目
    subgraph PHASE1 ["📖 第一阶段：理解题目 (Understanding the Problem)"]
        direction TB
        
        P1_START[开始理解题目]
        
        subgraph P1_CORE ["核心问题"]
            P1_Q1["❓ 未知量是什么？<br/>(What is the unknown?)"]
            P1_Q2["📊 已知数据是什么？<br/>(What are the data?)"]
            P1_Q3["⚖️ 条件是什么？<br/>(What is the condition?)"]
            P1_Q4["🔍 条件是否足够？<br/>是否有不充分、多余或矛盾？"]
        end
        
        subgraph P1_TOOLS ["理解工具"]
            P1_T1["🎨 画一张图<br/>(Draw a figure)"]
            P1_T2["🔤 引入合适的符号<br/>(Introduce notation)"]
            P1_T3["📝 分离条件各部分<br/>写下来"]
        end
        
        P1_CHECK{✅ 题目理解清楚了吗？}
        P1_RESULT["🎯 目标：问题结构化<br/>模糊→清晰"]
        
        P1_START --> P1_Q1
        P1_Q1 --> P1_Q2
        P1_Q2 --> P1_Q3
        P1_Q3 --> P1_Q4
        P1_Q4 --> P1_T1
        P1_T1 --> P1_T2
        P1_T2 --> P1_T3
        P1_T3 --> P1_CHECK
        P1_CHECK -->|否| P1_Q1
        P1_CHECK -->|是| P1_RESULT
    end
    
    %% 第二阶段：拟定方案
    subgraph PHASE2 ["🧠 第二阶段：拟定方案 (Devising a Plan)"]
        direction TB
        
        P2_START[开始制定计划]
        
        subgraph P2_RECALL ["回忆与联想"]
            P2_R1["🔄 见过类似问题吗？<br/>(Have you seen it before?)"]
            P2_R2["📚 知道相关定理吗？<br/>(Know a useful theorem?)"]
            P2_R3["🎯 审视未知量<br/>想相似问题"]
            P2_R4["💡 能利用已解决的<br/>相关问题吗？"]
        end
        
        subgraph P2_HEURISTICS ["启发式技巧"]
            P2_H1["🔍 特殊化<br/>(Specialization)<br/>简化问题"]
            P2_H2["🌐 一般化<br/>(Generalization)<br/>扩展问题"]
            P2_H3["🔗 类比法<br/>(Analogy)<br/>寻找相似结构"]
            P2_H4["⬅️ 逆向工作<br/>(Working backward)<br/>从目标出发"]
            P2_H5["🧩 分解问题<br/>(Decomposition)<br/>化整为零"]
            P2_H6["➕ 引入辅助元素<br/>(Auxiliary elements)<br/>辅助线、变量等"]
        end
        
        subgraph P2_STRATEGY ["策略选择"]
            P2_S1["📋 制定具体计划"]
            P2_S2["🔧 选择解题方法"]
            P2_S3["📊 确定解题步骤"]
        end
        
        P2_CHECK{💭 有可行方案了吗？}
        P2_RESULT["🎯 目标：建立已知与未知<br/>之间的联系"]
        
        P2_START --> P2_R1
        P2_R1 --> P2_R2
        P2_R2 --> P2_R3
        P2_R3 --> P2_R4
        P2_R4 --> P2_H1
        P2_H1 --> P2_H2
        P2_H2 --> P2_H3
        P2_H3 --> P2_H4
        P2_H4 --> P2_H5
        P2_H5 --> P2_H6
        P2_H6 --> P2_S1
        P2_S1 --> P2_S2
        P2_S2 --> P2_S3
        P2_S3 --> P2_CHECK
        P2_CHECK -->|否| P2_H1
        P2_CHECK -->|是| P2_RESULT
    end
    
    %% 第三阶段：执行方案
    subgraph PHASE3 ["⚡ 第三阶段：执行方案 (Carrying Out the Plan)"]
        direction TB
        
        P3_START[开始执行计划]
        
        subgraph P3_EXECUTE ["执行过程"]
            P3_E1["📝 按步骤执行"]
            P3_E2["🔍 检查每一步<br/>(Check each step)"]
            P3_E3["❓ 这步正确吗？<br/>(Is the step correct?)"]
            P3_E4["📐 能证明正确吗？<br/>(Can you prove it?)"]
        end
        
        subgraph P3_MONITOR ["监控验证"]
            P3_M1["⚠️ 保持警惕"]
            P3_M2["🧮 仔细计算"]
            P3_M3["📏 严格推理"]
            P3_M4["🔄 及时纠错"]
        end
        
        P3_CHECK{✅ 执行完成了吗？}
        P3_RESULT["🎯 目标：准确无误地<br/>实施解题方案"]
        
        P3_START --> P3_E1
        P3_E1 --> P3_E2
        P3_E2 --> P3_E3
        P3_E3 --> P3_E4
        P3_E4 --> P3_M1
        P3_M1 --> P3_M2
        P3_M2 --> P3_M3
        P3_M3 --> P3_M4
        P3_M4 --> P3_CHECK
        P3_CHECK -->|否| P3_E2
        P3_CHECK -->|是| P3_RESULT
    end
    
    %% 第四阶段：回顾检验
    subgraph PHASE4 ["🔍 第四阶段：回顾检验 (Looking Back)"]
        direction TB
        
        P4_START[开始回顾]
        
        subgraph P4_VERIFY ["验证结果"]
            P4_V1["✅ 能检验结果吗？<br/>(Can you check the result?)"]
            P4_V2["📋 能检验论证吗？<br/>(Can you check the argument?)"]
            P4_V3["🔄 能用不同方法吗？<br/>(Derive it differently?)"]
            P4_V4["👁️ 能一眼看出吗？<br/>(See it at a glance?)"]
        end
        
        subgraph P4_REFLECT ["反思总结"]
            P4_R1["🎓 学到了什么？"]
            P4_R2["🔧 方法可复用吗？"]
            P4_R3["🌟 能推广应用吗？<br/>(Use for other problems?)"]
            P4_R4["📚 形成知识模式"]
        end
        
        subgraph P4_IMPROVE ["改进提升"]
            P4_I1["💡 有更优解法吗？"]
            P4_I2["🎯 解题思路优化"]
            P4_I3["📈 技能提升记录"]
        end
        
        P4_RESULT["🎯 目标：从题目中学习<br/>提升解题能力"]
        
        P4_START --> P4_V1
        P4_V1 --> P4_V2
        P4_V2 --> P4_V3
        P4_V3 --> P4_V4
        P4_V4 --> P4_R1
        P4_R1 --> P4_R2
        P4_R2 --> P4_R3
        P4_R3 --> P4_R4
        P4_R4 --> P4_I1
        P4_I1 --> P4_I2
        P4_I2 --> P4_I3
        P4_I3 --> P4_RESULT
    end
    
    %% 结束节点
    END([🏆 解题完成<br/>知识内化])
    
    %% 主流程连接
    START --> PHASE1
    P1_RESULT --> PHASE2
    P2_RESULT --> PHASE3
    P3_RESULT --> PHASE4
    P4_RESULT --> END
    
    %% 反馈循环
    P2_CHECK -->|需要重新理解| PHASE1
    P3_CHECK -->|方案有问题| PHASE2
    P4_V2 -->|发现错误| PHASE3
    
    %% 核心技巧总结框
    subgraph TECHNIQUES ["🛠️ 波利亚核心技巧工具箱"]
        direction LR

        subgraph T_BASIC ["基础技巧"]
            T1["🔍 类比法<br/>(Analogy)"]
            T2["📊 特殊化<br/>(Specialization)"]
            T3["🌐 一般化<br/>(Generalization)"]
        end

        subgraph T_ADVANCED ["高级技巧"]
            T4["🧩 分解组合<br/>(Decomposition)"]
            T5["⬅️ 逆向工作<br/>(Working Backward)"]
            T6["🎨 画图建模<br/>(Visualization)"]
        end

        subgraph T_SYSTEMATIC ["系统方法"]
            T7["➕ 辅助元素<br/>(Auxiliary Elements)"]
            T8["🔄 试验检验<br/>(Trial & Check)"]
            T9["📝 重述问题<br/>(Restatement)"]
        end
    end

    %% 样式定义
    classDef startEnd fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px,color:#000,font-weight:bold
    classDef phase fill:#e3f2fd,stroke:#1565c0,stroke-width:2px,color:#000
    classDef question fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#000
    classDef tool fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    classDef check fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#000,font-weight:bold
    classDef result fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#000,font-weight:bold
    classDef technique fill:#fff8e1,stroke:#f57f17,stroke-width:2px,color:#000

    class START,END startEnd
    class P1_CHECK,P2_CHECK,P3_CHECK check
    class P1_RESULT,P2_RESULT,P3_RESULT,P4_RESULT result
    class T1,T2,T3,T4,T5,T6,T7,T8,T9 technique
```
